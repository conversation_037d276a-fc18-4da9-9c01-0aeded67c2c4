const { Client, LocalAuth, MessageAck } = require('whatsapp-web.js');
const qrcode = require('qrcode-terminal');
const fs = require('fs');
const path = require('path');

// 获取从Python传递的店铺信息
const STORE_NAME = process.env.STORE_NAME || 'DefaultStore';

// 获取店铺数据目录
const STORE_DATA_DIR = process.env.STORE_DATA_DIR || path.join(__dirname, 'store_data', STORE_NAME);

// 确保店铺数据目录存在
if (!fs.existsSync(STORE_DATA_DIR)) {
  fs.mkdirSync(STORE_DATA_DIR, { recursive: true });
}

// IPC文件路径
const IPC_FILE = process.env.IPC_FILE || path.join(STORE_DATA_DIR, 'whatsapp_messages.json');
const REPLY_FILE = path.join(STORE_DATA_DIR, 'whatsapp_replies.json');

console.log(`启动WhatsApp客服服务 - 店铺: ${STORE_NAME}`);
console.log(`店铺数据目录: ${STORE_DATA_DIR}`);
console.log(`IPC文件路径: ${IPC_FILE}`);
console.log(`回复文件路径: ${REPLY_FILE}`);

// 清空或创建消息和回复文件
fs.writeFileSync(IPC_FILE, JSON.stringify([]));
fs.writeFileSync(REPLY_FILE, JSON.stringify([]));

// 创建WhatsApp客户端实例，使用本地存储认证信息，这样只需要登录一次
// 注意：LocalAuth与userDataDir不兼容，只保留LocalAuth
const client = new Client({
  authStrategy: new LocalAuth({
    dataPath: path.join(STORE_DATA_DIR, 'session') // 使用店铺特定的会话目录
  }),
  puppeteer: {
    headless: false, // 设置为false可以显示浏览器窗口，更容易看到登录过程
    args: [
      '--no-sandbox',
      '--disable-setuid-sandbox',
      '--disable-web-security',
      '--disable-features=IsolateOrigins,site-per-process',
      '--ignore-certificate-errors',
      '--allow-running-insecure-content',
      '--window-size=1280,720',
      '--disable-gpu',
      '--disable-dev-shm-usage',
      '--disable-accelerated-2d-canvas',
      '--no-first-run',
      '--no-default-browser-check',
      '--disable-notifications'
    ],
    defaultViewport: null, // 使用默认视口大小
    timeout: 120000, // 增加超时时间到120秒
    // 优先使用内置Chromium，如果有自定义路径则使用自定义路径
    executablePath: process.env.PUPPETEER_EXECUTABLE_PATH || undefined
    // 移除userDataDir配置，因为它与LocalAuth不兼容
  }
});

// 存储接收到的消息
let receivedMessages = [];

// 已处理的输入消息ID集合，防止重复处理
const processedInputMessageIds = new Set();

// 将消息保存到IPC文件 - 改进版本，支持去重和自动清理
function saveMessageToFile(message) {
  try {
    const messageId = message.id._serialized;

    // 检查是否已经处理过这条消息
    if (processedInputMessageIds.has(messageId)) {
      console.log(`⏭️ 跳过已处理的输入消息: ${messageId}`);
      return;
    }

    // 标记消息为已处理
    processedInputMessageIds.add(messageId);

    // 读取当前文件内容
    let messages = [];
    if (fs.existsSync(IPC_FILE)) {
      const fileContent = fs.readFileSync(IPC_FILE, 'utf8');
      if (fileContent.trim()) {
        messages = JSON.parse(fileContent);
      }
    }

    // 处理消息内容和类型
    let processedBody = message.body;
    const messageType = message.type || 'chat';

    // 如果不是文本消息，替换消息内容
    if (messageType !== 'chat') {
      processedBody = `一条${messageType}类型的消息`;
      console.log(`检测到非文本消息类型: ${messageType}，已替换消息内容`);
    }

    // 创建新消息对象
    const newMessage = {
      id: messageId,
      from: message.from,
      body: processedBody,
      type: messageType,
      hasMedia: message.hasMedia || false,
      timestamp: Date.now(),
      processed: false,  // 标记是否已被Python处理
      // 如果是媒体消息，保存额外信息
      ...(message.hasMedia && {
        mimetype: message.mimetype,
        filename: message.filename,
        caption: message.caption
      })
    };

    // 添加新消息
    messages.push(newMessage);

    // 自动清理已处理的旧消息，保持文件大小合理
    const unprocessedMessages = messages.filter(msg => !msg.processed);
    const recentProcessedMessages = messages
      .filter(msg => msg.processed)
      .slice(-10); // 只保留最近10条已处理的消息作为历史记录

    const cleanedMessages = [...unprocessedMessages, ...recentProcessedMessages];

    // 写回文件
    fs.writeFileSync(IPC_FILE, JSON.stringify(cleanedMessages, null, 2));
    console.log(`✅ 新消息已保存: ${message.from} - ${processedBody} (类型: ${messageType})`);

    // 清理过期的输入消息ID（保留最近1000条）
    if (processedInputMessageIds.size > 1000) {
      const idsArray = Array.from(processedInputMessageIds);
      const keepIds = idsArray.slice(-500); // 保留最近500条
      processedInputMessageIds.clear();
      keepIds.forEach(id => processedInputMessageIds.add(id));
      console.log('🧹 已清理过期的输入消息ID');
    }

  } catch (error) {
    console.error('保存消息到文件时出错:', error);
  }
}

// 已处理的消息ID集合，防止重复发送
const processedMessageIds = new Set();

// 消息状态持久化文件
const MESSAGE_STATUS_FILE = path.join(STORE_DATA_DIR, 'message_status.json');

// 加载已处理的消息ID
function loadProcessedMessageIds() {
  try {
    if (fs.existsSync(MESSAGE_STATUS_FILE)) {
      const statusData = JSON.parse(fs.readFileSync(MESSAGE_STATUS_FILE, 'utf8'));
      if (statusData.processedIds && Array.isArray(statusData.processedIds)) {
        statusData.processedIds.forEach(id => processedMessageIds.add(id));
        console.log(`📋 已加载 ${statusData.processedIds.length} 个已处理消息ID`);
      }
    }
  } catch (error) {
    console.error('加载消息状态失败:', error.message);
  }
}

// 保存已处理的消息ID
function saveProcessedMessageIds() {
  try {
    const statusData = {
      processedIds: Array.from(processedMessageIds),
      lastUpdated: Date.now()
    };
    fs.writeFileSync(MESSAGE_STATUS_FILE, JSON.stringify(statusData, null, 2));
    console.log(`💾 已保存 ${statusData.processedIds.length} 个已处理消息ID`);
  } catch (error) {
    console.error('保存消息状态失败:', error.message);
  }
}

// 消息发送验证相关变量
const pendingMessages = new Map();
const MESSAGE_VERIFICATION_TIMEOUT = 10000; // 10秒超时

/**
 * 验证消息是否真正发送成功
 * @param {string} messageId - 消息ID
 * @param {string} recipient - 接收者
 * @param {string} content - 消息内容
 * @returns {Promise<boolean>} - 发送是否成功
 */
async function verifyMessageDelivery(messageId, recipient, content) {
  return new Promise((resolve) => {
    const timeoutId = setTimeout(() => {
      pendingMessages.delete(messageId);
      console.log(`消息发送验证超时 - 接收者: ${recipient}, 内容: ${content.substring(0, 30)}...`);
      resolve(false);
    }, MESSAGE_VERIFICATION_TIMEOUT);

    pendingMessages.set(messageId, {
      recipient,
      content: content.substring(0, 50),
      timestamp: Date.now(),
      timeoutId,
      resolve
    });
  });
}

/**
 * 处理消息ACK状态变化
 * @param {Object} message - 消息对象
 * @param {number} ack - ACK状态
 */
function handleMessageAck(message, ack) {
  const messageId = message.id._serialized;
  const pendingMessage = pendingMessages.get(messageId);

  if (!pendingMessage) {
    return;
  }

  if (ack === MessageAck.ACK_ERROR) {
    clearTimeout(pendingMessage.timeoutId);
    pendingMessages.delete(messageId);
    console.log(`消息发送失败 - 接收者: ${pendingMessage.recipient}, 内容: ${pendingMessage.content}...`);
    pendingMessage.resolve(false);
  } else if (ack >= MessageAck.ACK_SERVER) {
    clearTimeout(pendingMessage.timeoutId);
    pendingMessages.delete(messageId);
    const statusText = ack === MessageAck.ACK_SERVER ? '已到达服务器' :
                      ack === MessageAck.ACK_DEVICE ? '已到达设备' :
                      ack === MessageAck.ACK_READ ? '已读' : '已播放';
    console.log(`消息发送成功 (${statusText}) - 接收者: ${pendingMessage.recipient}, 内容: ${pendingMessage.content}...`);
    pendingMessage.resolve(true);
  }
}

// 清理过期的消息ID（保留最近24小时的记录）
function cleanupOldMessageIds() {
  try {
    if (fs.existsSync(MESSAGE_STATUS_FILE)) {
      const statusData = JSON.parse(fs.readFileSync(MESSAGE_STATUS_FILE, 'utf8'));
      const cutoffTime = Date.now() - (24 * 60 * 60 * 1000); // 24小时前

      if (statusData.lastUpdated && statusData.lastUpdated < cutoffTime) {
        // 清空过期的记录
        processedMessageIds.clear();
        fs.writeFileSync(MESSAGE_STATUS_FILE, JSON.stringify({ processedIds: [], lastUpdated: Date.now() }, null, 2));
        console.log('🧹 已清理过期的消息状态记录');
      }
    }
  } catch (error) {
    console.error('清理消息状态失败:', error.message);
  }
}

// 检查回复文件并发送消息 - 改进版本，支持消息去重和状态跟踪
async function checkAndSendReplies() {
  try {
    if (!fs.existsSync(REPLY_FILE)) {
      return;
    }

    const fileContent = fs.readFileSync(REPLY_FILE, 'utf8');
    if (!fileContent.trim()) {
      return;
    }

    let replies;
    try {
      replies = JSON.parse(fileContent);
    } catch (parseError) {
      console.error('解析回复文件失败:', parseError.message);
      return;
    }

    if (!replies || !Array.isArray(replies) || !replies.length) {
      return;
    }

    // 过滤掉已处理的消息（去重）
    const newReplies = replies.filter(reply => {
      const messageId = reply.id || `${reply.to}_${reply.timestamp}`;
      if (processedMessageIds.has(messageId)) {
        console.log(`⏭️ 跳过已处理的消息: ${messageId}`);
        return false;
      }
      return true;
    });

    if (!newReplies.length) {
      console.log('📝 所有消息都已处理过，跳过');
      return;
    }

    console.log(`发现 ${newReplies.length} 条新消息待发送`);

    // 按会话分组处理消息
    const sessionGroups = groupMessagesBySession(newReplies);

    // 处理每个会话的消息
    for (const [sessionId, sessionMessages] of Object.entries(sessionGroups)) {
      console.log(`📨 处理会话 ${sessionId}，共 ${sessionMessages.length} 条消息`);

      // 按段索引排序确保顺序发送
      sessionMessages.sort((a, b) => (a.segment_index || 0) - (b.segment_index || 0));

      const sessionResults = [];

      for (const reply of sessionMessages) {
        const messageId = reply.id || `${reply.to}_${reply.timestamp}`;
        const segmentInfo = reply.total_segments > 1 ?
          ` (${(reply.segment_index || 0) + 1}/${reply.total_segments})` : '';

        // 重试机制
        let success = false;
        let lastError = null;
        const maxRetries = 3;

        for (let attempt = 1; attempt <= maxRetries && !success; attempt++) {
          try {
            if (attempt > 1) {
              console.log(`🔄 重试发送消息到 ${reply.to}${segmentInfo} (第${attempt}次尝试)`);
              // 重试前等待更长时间，模拟人类遇到问题后的思考时间
              const retryDelay = Math.floor(Math.random() * 1000) + (2000 * attempt); // 基础延迟 + 随机延迟
              console.log(`⏱️ 重试等待时间: ${retryDelay}ms`);
              await new Promise(resolve => setTimeout(resolve, retryDelay));
            } else {
              console.log(`正在发送消息到 ${reply.to}${segmentInfo}: ${reply.message.substring(0, 50)}...`);
            }

            // 等待消息发送完成
            const sentMessage = await client.sendMessage(reply.to, reply.message);

            console.log(`消息已提交到WhatsApp到 ${reply.to}${segmentInfo}, 消息ID: ${sentMessage.id._serialized}`);

            // 验证消息是否真正发送成功
            const deliverySuccess = await verifyMessageDelivery(
              sentMessage.id._serialized,
              reply.to,
              reply.message
            );

            if (deliverySuccess) {
              console.log(`消息真实发送成功到 ${reply.to}${segmentInfo}, 消息ID: ${sentMessage.id._serialized}`);

              // 标记消息为已处理
              processedMessageIds.add(messageId);

              // 立即保存状态，防止重复处理
              saveProcessedMessageIds();

              sessionResults.push({
                success: true,
                messageId: messageId,
                to: reply.to,
                whatsappMessageId: sentMessage.id._serialized,
                attempts: attempt
              });

              success = true;
            } else {
              console.log(`消息发送验证失败到 ${reply.to}${segmentInfo}, 消息ID: ${sentMessage.id._serialized}`);

              // 如果是最后一次尝试，记录失败结果
              if (attempt === maxRetries) {
                sessionResults.push({
                  success: false,
                  messageId: messageId,
                  to: reply.to,
                  error: '消息发送验证失败',
                  attempts: attempt
                });
              }
            }

          } catch (error) {
            lastError = error;
            console.error(`❌ 发送消息失败到 ${reply.to}${segmentInfo} (第${attempt}次尝试):`, error.message);

            // 如果是最后一次尝试，记录失败结果
            if (attempt === maxRetries) {
              sessionResults.push({
                success: false,
                messageId: messageId,
                to: reply.to,
                error: error.message,
                attempts: attempt
              });
            }
          }
        }

        // 模拟人类发送间隔，避免被识别为AI自动回复
        if (sessionMessages.length > 1) {
          // 1-3秒随机间隔，模拟人类思考和打字时间
          const humanLikeDelay = Math.floor(Math.random() * 2000) + 1000; // 1000-3000ms (1-3秒)
          console.log(`⏱️ 模拟人类发送间隔: ${humanLikeDelay}ms`);
          await new Promise(resolve => setTimeout(resolve, humanLikeDelay));
        }
      }

      const successCount = sessionResults.filter(r => r.success).length;
      console.log(`📊 会话 ${sessionId} 发送完成: 成功 ${successCount}/${sessionResults.length}`);

      // 保存消息状态
      saveProcessedMessageIds();

      // 会话间隔，模拟人类在不同对话间的切换时间
      const sessionSwitchDelay = Math.floor(Math.random() * 1500) + 500; // 500-2000ms (0.5-2秒)
      console.log(`🔄 会话切换间隔: ${sessionSwitchDelay}ms`);
      await new Promise(resolve => setTimeout(resolve, sessionSwitchDelay));
    }

    // 立即清空文件，防止重复处理
    fs.writeFileSync(REPLY_FILE, JSON.stringify([]));
    console.log(`📝 所有消息已发送完成，回复文件已清空`);

  } catch (error) {
    console.error('处理回复文件时出错:', error);
  }
}

// 按会话ID分组消息
function groupMessagesBySession(messages) {
  const groups = {};

  for (const message of messages) {
    const sessionId = message.session_id || 'default';
    if (!groups[sessionId]) {
      groups[sessionId] = [];
    }
    groups[sessionId].push(message);
  }

  return groups;
}

// 设置文件监听器，避免定时器重复处理
function setupFileWatcher() {
  let isProcessing = false;
  let lastProcessTime = 0;

  // 使用文件监听而不是定时器
  if (fs.existsSync(REPLY_FILE)) {
    fs.watchFile(REPLY_FILE, { interval: 500 }, async (curr, prev) => {
      // 防止重复处理
      if (isProcessing) {
        console.log('⏸️ 正在处理中，跳过此次文件变化');
        return;
      }

      // 防止频繁触发
      const now = Date.now();
      if (now - lastProcessTime < 1000) {
        console.log('⏸️ 处理间隔太短，跳过此次文件变化');
        return;
      }

      // 检查文件是否有内容
      if (curr.size === 0) {
        console.log('📝 文件为空，跳过处理');
        return;
      }

      console.log('📁 检测到回复文件变化，开始处理...');
      isProcessing = true;
      lastProcessTime = now;

      try {
        await checkAndSendReplies();
      } catch (error) {
        console.error('处理回复文件时出错:', error);
      } finally {
        isProcessing = false;
      }
    });
  }

  // 备用定时器，频率降低到10秒，仅作为保险
  setInterval(async () => {
    if (!isProcessing && fs.existsSync(REPLY_FILE)) {
      const stats = fs.statSync(REPLY_FILE);
      if (stats.size > 0) {
        console.log('🔄 备用定时器检查到待处理消息');
        isProcessing = true;
        try {
          await checkAndSendReplies();
        } catch (error) {
          console.error('备用定时器处理时出错:', error);
        } finally {
          isProcessing = false;
        }
      }
    }
  }, 10000); // 10秒检查一次作为备用

  console.log('📁 文件监听器已设置');
}

// 当需要扫码登录时，将二维码展示在终端
client.on('qr', (qr) => {
  console.log('请使用WhatsApp扫描以下二维码登录:');
  qrcode.generate(qr, { small: true });
});

// 客户端准备加载中
client.on('loading_screen', (percent, message) => {
  console.log('加载中... ', percent, '%', message);
});

// 客户端已认证（不需要扫码）
client.on('authenticated', () => {
  console.log('认证成功!');
});

// 认证失败
client.on('auth_failure', (msg) => {
  console.error('认证失败:', msg);
});

// 客户端已准备好
client.on('ready', () => {
  console.log('客户端已准备就绪，开始监听新消息!');

  // 加载已处理的消息状态
  loadProcessedMessageIds();

  // 清理过期的消息ID
  cleanupOldMessageIds();

  // 检查所有未读消息
  checkUnreadMessages();

  // 设置文件监听，只在文件变化时处理
  setupFileWatcher();
});

// 监听新消息
client.on('message', async (message) => {
  console.log(`收到来自 ${message.from} 的新消息: ${message.body}`);

  // 保存消息到文件以便Python处理
  saveMessageToFile(message);
});

// 监听消息ACK状态变化
client.on('message_ack', (message, ack) => {
  handleMessageAck(message, ack);
});

// 监听断开连接事件
client.on('disconnected', (reason) => {
  console.log('客户端已断开连接:', reason);
  // 重连逻辑
  console.log('正在尝试重新连接...');
  client.initialize();
});

// 检查并处理所有未读消息 - 改进版本，避免重复处理
async function checkUnreadMessages() {
  console.log('正在检查未读消息...');

  try {
    // 获取所有聊天
    const chats = await client.getChats();

    // 筛选出有未读消息的聊天
    const unreadChats = chats.filter(chat => chat.unreadCount > 0);

    console.log(`发现 ${unreadChats.length} 个聊天有未读消息`);

    let totalProcessed = 0;
    let totalSkipped = 0;

    // 处理每个有未读消息的聊天
    for (const chat of unreadChats) {
      console.log(`检查来自 ${chat.name} 的 ${chat.unreadCount} 条未读消息`);

      try {
        // 获取聊天历史消息
        const messages = await chat.fetchMessages({
          limit: chat.unreadCount // 只获取未读数量的消息
        });

        let chatProcessed = 0;
        let chatSkipped = 0;

        // 保存每条未读消息以便Python处理
        for (const message of messages) {
          const messageId = message.id._serialized;

          // 检查是否已经处理过
          if (processedInputMessageIds.has(messageId)) {
            chatSkipped++;
            continue;
          }

          // 只处理真正的新消息
          saveMessageToFile(message);
          chatProcessed++;
        }

        totalProcessed += chatProcessed;
        totalSkipped += chatSkipped;

        console.log(`聊天 ${chat.name}: 处理 ${chatProcessed} 条新消息，跳过 ${chatSkipped} 条已处理消息`);

      } catch (chatError) {
        console.error(`处理聊天 ${chat.name} 时出错:`, chatError.message);
      }
    }

    console.log(`✅ 未读消息检查完成: 处理 ${totalProcessed} 条新消息，跳过 ${totalSkipped} 条重复消息`);

  } catch (error) {
    console.error('处理未读消息时出错:', error);
  }
}

// 初始化客户端
client.initialize();

console.log(`WhatsApp客服服务已启动 - ${STORE_NAME}`);
