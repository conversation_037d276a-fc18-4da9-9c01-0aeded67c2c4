
const { Client, LocalAuth, MessageAck } = require('whatsapp-web.js');
const fs = require('fs');
const path = require('path');

// 获取环境变量
const BULK_DATA_DIR = process.env.BULK_DATA_DIR || './data/bulk_messaging';
const STATUS_FILE = path.join(BULK_DATA_DIR, 'status.json');
const SEND_QUEUE_FILE = path.join(BULK_DATA_DIR, 'send_queue.json');
const SEND_RESULTS_FILE = path.join(BULK_DATA_DIR, 'send_results.json');

// 确保数据目录存在
if (!fs.existsSync(BULK_DATA_DIR)) {
    fs.mkdirSync(BULK_DATA_DIR, { recursive: true });
}

// 初始化状态文件
function updateStatus(status) {
    const statusData = {
        timestamp: Date.now(),
        ...status
    };
    fs.writeFileSync(STATUS_FILE, JSON.stringify(statusData, null, 2));
}

// 消息发送验证相关变量
const pendingMessages = new Map();
const MESSAGE_VERIFICATION_TIMEOUT = 10000; // 10秒超时

/**
 * 验证消息是否真正发送成功
 * @param {string} messageId - 消息ID
 * @param {string} recipient - 接收者
 * @param {string} content - 消息内容
 * @returns {Promise<boolean>} - 发送是否成功
 */
async function verifyMessageDelivery(messageId, recipient, content) {
    return new Promise((resolve) => {
        const timeoutId = setTimeout(() => {
            pendingMessages.delete(messageId);
            console.log(`群发消息发送验证超时 - 接收者: ${recipient}, 内容: ${content.substring(0, 30)}...`);
            resolve(false);
        }, MESSAGE_VERIFICATION_TIMEOUT);

        pendingMessages.set(messageId, {
            recipient,
            content: content.substring(0, 50),
            timestamp: Date.now(),
            timeoutId,
            resolve
        });
    });
}

/**
 * 处理消息ACK状态变化
 * @param {Object} message - 消息对象
 * @param {number} ack - ACK状态
 */
function handleMessageAck(message, ack) {
    const messageId = message.id._serialized;
    const pendingMessage = pendingMessages.get(messageId);

    if (!pendingMessage) {
        return;
    }

    if (ack === MessageAck.ACK_ERROR) {
        clearTimeout(pendingMessage.timeoutId);
        pendingMessages.delete(messageId);
        console.log(`群发消息发送失败 - 接收者: ${pendingMessage.recipient}, 内容: ${pendingMessage.content}...`);
        pendingMessage.resolve(false);
    } else if (ack >= MessageAck.ACK_SERVER) {
        clearTimeout(pendingMessage.timeoutId);
        pendingMessages.delete(messageId);
        const statusText = ack === MessageAck.ACK_SERVER ? '已到达服务器' :
                          ack === MessageAck.ACK_DEVICE ? '已到达设备' :
                          ack === MessageAck.ACK_READ ? '已读' : '已播放';
        console.log(`群发消息发送成功 (${statusText}) - 接收者: ${pendingMessage.recipient}, 内容: ${pendingMessage.content}...`);
        pendingMessage.resolve(true);
    }
}

// 创建WhatsApp客户端
const client = new Client({
    authStrategy: new LocalAuth({
        clientId: 'bulk-messaging',
        dataPath: path.join(BULK_DATA_DIR, 'auth')
    }),
    puppeteer: {
        headless: false,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    }
});

// 客户端事件处理
client.on('qr', (qr) => {
    console.log('QR Code received:', qr);
    updateStatus({
        status: 'qr_received',
        qr_code: qr,
        is_logged_in: false
    });
});

client.on('ready', () => {
    console.log('WhatsApp client is ready!');
    updateStatus({
        status: 'ready',
        is_logged_in: true
    });
    
    // 开始监听发送队列
    startSendQueueMonitor();
});

client.on('authenticated', () => {
    console.log('WhatsApp client authenticated');
    updateStatus({
        status: 'authenticated',
        is_logged_in: true
    });
});

client.on('auth_failure', (msg) => {
    console.error('Authentication failed:', msg);
    updateStatus({
        status: 'auth_failure',
        error: msg,
        is_logged_in: false
    });
});

client.on('disconnected', (reason) => {
    console.log('WhatsApp client disconnected:', reason);
    updateStatus({
        status: 'disconnected',
        reason: reason,
        is_logged_in: false
    });
});

// 监听消息ACK状态变化
client.on('message_ack', (message, ack) => {
    handleMessageAck(message, ack);
});

// 发送队列监控
function startSendQueueMonitor() {
    setInterval(() => {
        if (fs.existsSync(SEND_QUEUE_FILE)) {
            try {
                const queueData = JSON.parse(fs.readFileSync(SEND_QUEUE_FILE, 'utf8'));
                if (queueData.action === 'send_bulk' && queueData.numbers && queueData.message) {
                    processBulkSend(queueData);
                    // 清空队列文件
                    fs.writeFileSync(SEND_QUEUE_FILE, '{}');
                }
            } catch (error) {
                console.error('Error processing send queue:', error);
            }
        }
    }, 1000);
}

// 处理群发消息
async function processBulkSend(queueData) {
    const { numbers, message } = queueData;
    const results = [];

    console.log(`Starting bulk send to ${numbers.length} numbers`);

    for (let i = 0; i < numbers.length; i++) {
        const number = numbers[i];
        try {
            // 发送消息
            const sentMessage = await client.sendMessage(number, message);

            console.log(`群发消息已提交到WhatsApp到 ${number} (${i + 1}/${numbers.length}), 消息ID: ${sentMessage.id._serialized}`);

            // 验证消息是否真正发送成功
            const deliverySuccess = await verifyMessageDelivery(
                sentMessage.id._serialized,
                number,
                message
            );

            if (deliverySuccess) {
                results.push({
                    number: number,
                    status: 'success',
                    timestamp: Date.now(),
                    messageId: sentMessage.id._serialized
                });

                console.log(`群发消息真实发送成功到 ${number} (${i + 1}/${numbers.length})`);
            } else {
                results.push({
                    number: number,
                    status: 'failed',
                    error: '消息发送验证失败',
                    timestamp: Date.now(),
                    messageId: sentMessage.id._serialized
                });

                console.log(`群发消息发送验证失败到 ${number} (${i + 1}/${numbers.length})`);
            }

            // 更新进度
            updateStatus({
                status: 'sending',
                progress: i + 1,
                total: numbers.length,
                current_number: number
            });

            // 发送间隔（避免被限制） - 1-2秒随机延迟
            if (i < numbers.length - 1) {
                const randomDelay = Math.floor(Math.random() * 1000) + 1000; // 1000-2000ms (1-2秒)
                await new Promise(resolve => setTimeout(resolve, randomDelay));
            }

        } catch (error) {
            console.error(`Failed to send message to ${number}:`, error);

            // 详细的错误分析和日志
            let errorCategory = 'unknown';
            let errorDetails = error.message;

            if (error.message.includes('phone number is not registered')) {
                errorCategory = 'not_registered';
                errorDetails = '该号码未注册WhatsApp';
            } else if (error.message.includes('invalid phone number')) {
                errorCategory = 'invalid_format';
                errorDetails = '号码格式无效';
            } else if (error.message.includes('blocked')) {
                errorCategory = 'blocked';
                errorDetails = '号码被阻止或限制';
            } else if (error.message.includes('rate limit')) {
                errorCategory = 'rate_limit';
                errorDetails = '发送频率过快，触发限制';
            } else if (error.message.includes('network')) {
                errorCategory = 'network';
                errorDetails = '网络连接问题';
            } else if (error.message.includes('session')) {
                errorCategory = 'session';
                errorDetails = 'WhatsApp会话问题';
            }

            console.log(`群发消息发送失败详细分析 - 号码: ${number}, 错误类型: ${errorCategory}, 详情: ${errorDetails}`);

            results.push({
                number: number,
                status: 'failed',
                error: error.message,
                errorCategory: errorCategory,
                errorDetails: errorDetails,
                timestamp: Date.now()
            });

            // 更新进度（即使失败也要更新）
            updateStatus({
                status: 'sending',
                progress: i + 1,
                total: numbers.length,
                current_number: number,
                error: error.message,
                errorCategory: errorCategory
            });

            // 失败后也要延迟，避免连续失败
            if (i < numbers.length - 1) {
                const randomDelay = Math.floor(Math.random() * 1000) + 1000;
                await new Promise(resolve => setTimeout(resolve, randomDelay));
            }
        }
    }

    // 保存发送结果
    fs.writeFileSync(SEND_RESULTS_FILE, JSON.stringify(results, null, 2));

    // 更新最终状态
    const successCount = results.filter(r => r.status === 'success').length;
    const failedCount = results.filter(r => r.status === 'failed').length;

    updateStatus({
        status: 'send_completed',
        total_sent: numbers.length,
        success_count: successCount,
        failed_count: failedCount
    });

    console.log(`Bulk send completed. Success: ${successCount}, Failed: ${failedCount}`);
}

// 启动客户端
console.log('Starting WhatsApp bulk messaging service...');
updateStatus({
    status: 'starting',
    is_logged_in: false
});

client.initialize();

// 优雅关闭处理
process.on('SIGINT', () => {
    console.log('Shutting down WhatsApp bulk messaging service...');
    updateStatus({
        status: 'shutting_down',
        is_logged_in: false
    });
    client.destroy();
    process.exit(0);
});
